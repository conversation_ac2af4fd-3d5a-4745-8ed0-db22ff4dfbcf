<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ReturnModel;

echo "=== Simple Return Test ===\n";

// Find the return we just approved
$return = ReturnModel::where('status', 'approved')
    ->whereNull('store_id')
    ->whereNotNull('supplier_id')
    ->first();

if ($return) {
    echo "Found approved return: {$return->id}\n";
    echo "Current status: {$return->status}\n";
    
    // Simulate supplier accepting the return
    $return->update([
        'status' => 'in_transit',
        'admin_notes' => ($return->admin_notes ?? '') . "\n\nSupplier Response: Accepted",
    ]);
    
    echo "Updated status to: {$return->status}\n";
    
    // Simulate admin completing the return
    $return->update([
        'status' => 'completed',
        'completed_date' => now(),
        'admin_notes' => ($return->admin_notes ?? '') . "\n\nCompleted: Return finished",
    ]);
    
    echo "Final status: {$return->status}\n";
    echo "Completed date: {$return->completed_date}\n";
} else {
    echo "No approved return found\n";
}

echo "\n=== Test Complete ===\n";
