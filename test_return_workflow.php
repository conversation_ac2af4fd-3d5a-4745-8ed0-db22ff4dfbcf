<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\ReturnModel;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\User;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Return Workflow Test ===\n\n";

// Check current returns
echo "Current returns in database:\n";
$returns = ReturnModel::with(['product', 'supplier'])->get();
echo "Total returns: " . $returns->count() . "\n";

foreach ($returns as $return) {
    echo "- ID: {$return->id}\n";
    echo "  Product: " . ($return->product->name ?? 'N/A') . "\n";
    echo "  Supplier: " . ($return->supplier->name ?? 'N/A') . "\n";
    echo "  Status: {$return->status}\n";
    echo "  Store ID: " . ($return->store_id ?? 'NULL') . "\n";
    echo "  Return Date: {$return->return_date}\n";
    echo "  Approved Date: " . ($return->approved_date ?? 'NULL') . "\n";
    echo "  Completed Date: " . ($return->completed_date ?? 'NULL') . "\n";
    echo "\n";
}

// Check available suppliers and products for testing
echo "\nAvailable suppliers:\n";
$suppliers = Supplier::take(3)->get();
foreach ($suppliers as $supplier) {
    echo "- {$supplier->name} (ID: {$supplier->id})\n";
}

echo "\nAvailable products:\n";
$products = Product::take(3)->get();
foreach ($products as $product) {
    echo "- {$product->name} (ID: {$product->id})\n";
}

echo "\nAvailable admin users:\n";
$admins = User::where('role', 'admin')->take(2)->get();
foreach ($admins as $admin) {
    echo "- {$admin->name} (ID: {$admin->id})\n";
}

// Test the filtering logic for admin return history
echo "\n=== Testing Admin Return History Filtering ===\n";
$adminHistoryQuery = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
$adminHistoryQuery->whereNull('store_id')->whereNotNull('supplier_id');
$adminHistoryQuery->whereIn('status', ['completed', 'rejected']);

$currentMonth = \Carbon\Carbon::now()->format('Y-m');
$startDate = \Carbon\Carbon::createFromFormat('Y-m', $currentMonth)->startOfMonth();
$endDate = \Carbon\Carbon::createFromFormat('Y-m', $currentMonth)->endOfMonth();

$adminHistoryQuery->where(function($q) use ($startDate, $endDate) {
    $q->where(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'completed')
             ->whereNotNull('completed_date')
             ->whereBetween('completed_date', [$startDate, $endDate]);
    })->orWhere(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'rejected')
             ->whereNotNull('approved_date')
             ->whereBetween('approved_date', [$startDate, $endDate]);
    });
});

$adminHistoryReturns = $adminHistoryQuery->get();
echo "Admin history returns (should be empty): " . $adminHistoryReturns->count() . "\n";

// Test the filtering logic for supplier active returns
echo "\n=== Testing Supplier Active Returns Filtering ===\n";
$supplierId = $suppliers->first()->id;
$supplierActiveQuery = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
$supplierActiveQuery->whereBetween('return_date', [$startDate, $endDate]);
$supplierActiveQuery->where('supplier_id', $supplierId);
$supplierActiveQuery->whereIn('status', ['approved', 'in_transit']);

$supplierActiveReturns = $supplierActiveQuery->get();
echo "Supplier active returns: " . $supplierActiveReturns->count() . "\n";
foreach ($supplierActiveReturns as $return) {
    echo "- Status: {$return->status}, Store ID: " . ($return->store_id ?? 'NULL') . "\n";
}

// Test the filtering logic for supplier history
echo "\n=== Testing Supplier History Filtering ===\n";
$supplierHistoryQuery = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
$supplierHistoryQuery->where('supplier_id', $supplierId);
$supplierHistoryQuery->whereIn('status', ['completed', 'rejected']);

$supplierHistoryQuery->where(function($q) use ($startDate, $endDate) {
    $q->where(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'completed')
             ->whereNotNull('completed_date')
             ->whereBetween('completed_date', [$startDate, $endDate]);
    })->orWhere(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'rejected')
             ->whereBetween('updated_at', [$startDate, $endDate]);
    });
});

$supplierHistoryReturns = $supplierHistoryQuery->get();
echo "Supplier history returns (should be empty): " . $supplierHistoryReturns->count() . "\n";

echo "\n=== Test Complete ===\n";
