<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\ReturnModel;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\User;
use App\Http\Controllers\Admin\AdminReturnController;
use App\Http\Controllers\Supplier\SupplierReturnController;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Return Controller Workflow Test ===\n\n";

// Get the warehouse return that's in 'requested' status
$warehouseReturn = ReturnModel::whereNull('store_id')
    ->whereNotNull('supplier_id')
    ->where('status', 'requested')
    ->first();

if (!$warehouseReturn) {
    echo "No warehouse return found in 'requested' status. Creating one...\n";
    
    $supplier = Supplier::first();
    $product = Product::first();
    $admin = User::where('role', 'admin')->first();
    
    $warehouseReturn = ReturnModel::create([
        'product_id' => $product->id,
        'supplier_id' => $supplier->id,
        'store_id' => null, // Warehouse return
        'quantity' => 5,
        'reason' => 'damaged',
        'description' => 'Test warehouse return for workflow testing',
        'status' => 'requested',
        'return_date' => now(),
        'requested_by' => $admin->id,
    ]);
    
    echo "Created warehouse return: {$warehouseReturn->id}\n";
}

echo "Testing with warehouse return ID: {$warehouseReturn->id}\n";
echo "Current status: {$warehouseReturn->status}\n\n";

// Test 1: Check admin return history (should be empty)
echo "=== Test 1: Admin Return History (should be empty) ===\n";
$adminController = new AdminReturnController();
$request = new Request();

// Simulate the history method logic
$query = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
$query->whereNull('store_id')->whereNotNull('supplier_id');
$query->whereIn('status', ['completed', 'rejected']);

$currentMonth = \Carbon\Carbon::now()->format('Y-m');
$startDate = \Carbon\Carbon::createFromFormat('Y-m', $currentMonth)->startOfMonth();
$endDate = \Carbon\Carbon::createFromFormat('Y-m', $currentMonth)->endOfMonth();

$query->where(function($q) use ($startDate, $endDate) {
    $q->where(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'completed')
             ->whereNotNull('completed_date')
             ->whereBetween('completed_date', [$startDate, $endDate]);
    })->orWhere(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'rejected')
             ->whereNotNull('approved_date')
             ->whereBetween('approved_date', [$startDate, $endDate]);
    });
});

$historyReturns = $query->get();
echo "Admin history returns count: " . $historyReturns->count() . " (should be 0)\n\n";

// Test 2: Approve the warehouse return
echo "=== Test 2: Approve Warehouse Return ===\n";
$warehouseReturn->update([
    'status' => 'approved',
    'approved_date' => now(),
    'approved_by' => User::where('role', 'admin')->first()->id,
    'admin_notes' => 'Approved for testing workflow',
]);

$warehouseReturn->refresh();
echo "Return status after approval: {$warehouseReturn->status}\n";
echo "Approved date: {$warehouseReturn->approved_date}\n\n";

// Test 3: Check supplier active returns (should show the approved return)
echo "=== Test 3: Supplier Active Returns (should show approved return) ===\n";
$supplierId = $warehouseReturn->supplier_id;

$supplierActiveQuery = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
$supplierActiveQuery->whereBetween('return_date', [$startDate, $endDate]);
$supplierActiveQuery->where('supplier_id', $supplierId);
$supplierActiveQuery->whereIn('status', ['approved', 'in_transit']);

$supplierActiveReturns = $supplierActiveQuery->get();
echo "Supplier active returns count: " . $supplierActiveReturns->count() . " (should be >= 1)\n";
foreach ($supplierActiveReturns as $return) {
    echo "- Return ID: {$return->id}, Status: {$return->status}, Store ID: " . ($return->store_id ?? 'NULL') . "\n";
}
echo "\n";

// Test 4: Supplier accepts the return
echo "=== Test 4: Supplier Accepts Return ===\n";
$approvedReturn = $supplierActiveReturns->where('status', 'approved')->first();
if ($approvedReturn) {
    $approvedReturn->update([
        'status' => 'in_transit',
        'admin_notes' => ($approvedReturn->admin_notes ?? '') . "\n\nSupplier Response: Accepted for testing",
    ]);

    $approvedReturn->refresh();
    echo "Return status after supplier acceptance: {$approvedReturn->status}\n";
} else {
    echo "No approved return found to test supplier response\n";
}
echo "\n";

// Test 5: Check supplier active returns after acceptance (should still show in_transit)
echo "=== Test 5: Supplier Active Returns After Acceptance ===\n";
$supplierActiveQuery2 = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
$supplierActiveQuery2->whereBetween('return_date', [$startDate, $endDate]);
$supplierActiveQuery2->where('supplier_id', $supplierId);
$supplierActiveQuery2->whereIn('status', ['approved', 'in_transit']);

$supplierActiveReturns2 = $supplierActiveQuery2->get();
echo "Supplier active returns count: " . $supplierActiveReturns2->count() . "\n";
foreach ($supplierActiveReturns2 as $return) {
    echo "- Return ID: {$return->id}, Status: {$return->status}, Store ID: " . ($return->store_id ?? 'NULL') . "\n";
}
echo "\n";

// Test 6: Check supplier history (should still be empty)
echo "=== Test 6: Supplier History (should still be empty) ===\n";
$supplierHistoryQuery = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
$supplierHistoryQuery->where('supplier_id', $supplierId);
$supplierHistoryQuery->whereIn('status', ['completed', 'rejected']);

$supplierHistoryQuery->where(function($q) use ($startDate, $endDate) {
    $q->where(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'completed')
             ->whereNotNull('completed_date')
             ->whereBetween('completed_date', [$startDate, $endDate]);
    })->orWhere(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'rejected')
             ->whereBetween('updated_at', [$startDate, $endDate]);
    });
});

$supplierHistoryReturns = $supplierHistoryQuery->get();
echo "Supplier history returns count: " . $supplierHistoryReturns->count() . " (should be 0)\n\n";

// Test 7: Admin completes the return
echo "=== Test 7: Admin Completes Return ===\n";
if ($approvedReturn) {
    $approvedReturn->update([
        'status' => 'completed',
        'completed_date' => now(),
        'admin_notes' => ($approvedReturn->admin_notes ?? '') . "\n\nCompleted: Return processing finished",
    ]);

    $approvedReturn->refresh();
    echo "Return status after admin completion: {$approvedReturn->status}\n";
    echo "Completed date: {$approvedReturn->completed_date}\n";
}
echo "\n";

// Test 8: Check admin history after completion (should show the completed return)
echo "=== Test 8: Admin History After Completion ===\n";
$adminHistoryQuery2 = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
$adminHistoryQuery2->whereNull('store_id')->whereNotNull('supplier_id');
$adminHistoryQuery2->whereIn('status', ['completed', 'rejected']);

$adminHistoryQuery2->where(function($q) use ($startDate, $endDate) {
    $q->where(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'completed')
             ->whereNotNull('completed_date')
             ->whereBetween('completed_date', [$startDate, $endDate]);
    })->orWhere(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'rejected')
             ->whereNotNull('approved_date')
             ->whereBetween('approved_date', [$startDate, $endDate]);
    });
});

$adminHistoryReturns2 = $adminHistoryQuery2->get();
echo "Admin history returns count: " . $adminHistoryReturns2->count() . " (should be 1)\n";
foreach ($adminHistoryReturns2 as $return) {
    echo "- Return ID: {$return->id}, Status: {$return->status}, Completed: {$return->completed_date}\n";
}
echo "\n";

// Test 9: Check supplier history after completion (should show the completed return)
echo "=== Test 9: Supplier History After Completion ===\n";
$supplierHistoryQuery2 = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
$supplierHistoryQuery2->where('supplier_id', $supplierId);
$supplierHistoryQuery2->whereIn('status', ['completed', 'rejected']);

$supplierHistoryQuery2->where(function($q) use ($startDate, $endDate) {
    $q->where(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'completed')
             ->whereNotNull('completed_date')
             ->whereBetween('completed_date', [$startDate, $endDate]);
    })->orWhere(function($subQ) use ($startDate, $endDate) {
        $subQ->where('status', 'rejected')
             ->whereBetween('updated_at', [$startDate, $endDate]);
    });
});

$supplierHistoryReturns2 = $supplierHistoryQuery2->get();
echo "Supplier history returns count: " . $supplierHistoryReturns2->count() . " (should be 1)\n";
foreach ($supplierHistoryReturns2 as $return) {
    echo "- Return ID: {$return->id}, Status: {$return->status}, Completed: {$return->completed_date}\n";
}
echo "\n";

echo "=== Test Complete ===\n";
